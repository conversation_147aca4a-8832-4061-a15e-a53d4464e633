# LiteBusRpc 详细设计文档

## 1. 概述
LiteBusRpc 是一个面向嵌入式和资源受限环境的纯 C 语言实现的高性能 RPC 框架，通过 capnproto 编译器插件自动生成 C++/Rust 代码。框架采用 libev 作为核心事件库，提供高效的事件驱动和异步处理机制。支持 request/response（请求/响应）与 notify（通知）两种通信模式，每种模式都支持单播和广播通信，通过地址类型指定目标。底层数据发送与组包逻辑通过接口注入，便于适配多种物理链路和协议。框架核心库不处理具体的序列化逻辑，序列化由插件生成的代码实现，实现了清晰的职责分离。框架核心库自身无动态内存分配，但提供内存分配器接口以支持用户注入自定义分配策略，完全支持裸机/RTOS 环境。

### 1.1 核心特性
- **事件驱动架构**：基于 libev 实现高效的事件循环和异步 I/O 处理
- **异步消息处理**：支持异步消息发送、接收和超时处理
- **多传输层支持**：通过事件驱动机制同时管理多种物理传输层
- **职责分离设计**：核心库专注于消息路由，序列化交给插件生成的代码处理
- **插件式代码生成**：基于 capnproto 编译器插件架构，集成到标准工具链
- **定时器支持**：集成 libev 定时器实现请求超时和周期性任务
- **零动态内存分配**：核心库不进行动态内存分配，通过预分配和静态内存池实现

## 2. 架构设计

### 2.1 总体架构
- **核心库（纯 C 语言）**：实现协议栈、调度、接口抽象，核心库自身不进行动态内存分配。核心库不处理具体的序列化逻辑。
- **事件抽象层**：提供统一的事件驱动接口，完全隐藏底层事件库（如 libev）的实现细节和依赖。
- **libev 事件系统**：作为内部实现，提供底层事件循环、异步 I/O、定时器和信号处理机制。
- **事件驱动调度器**：基于抽象事件接口实现消息调度、超时处理和异步回调。
- **capnp plugin 代码生成器**：基于 capnproto 编译器插件架构，解析 schema 并生成 C++/Rust 结构体、序列化代码及 RPC 分发代码。
- **传输与组包接口**：通过函数指针抽象，用户可注入自定义传输层和组包实现，适配各种物理链路。

### 2.2 模块划分
- `lbrpc_core`：核心调度与消息处理，基于抽象事件接口。
- `lbrpc_event_abstract`：事件系统抽象层，提供统一的事件处理接口。
- `lbrpc_event_libev`：libev 事件系统实现（内部模块，用户不直接使用）。
- `lbrpc_transport`：传输层抽象与实现，通过抽象事件接口集成。
- `lbrpc_packetizer`：组包/拆包抽象与实现。
- `lbrpc_reqresp`：request/response 模式实现，支持异步超时。
- `lbrpc_notify`：notify 模式实现。
- `lbrpc_timer`：基于抽象接口的定时器服务。
- `lbrpc_codec_registry`：编解码器注册中心，管理不同类型的序列化接口。
- `lbrpc-capnp-plugin`：capnproto 编译器插件，生成多语言代码（支持 C++/Rust 输出）。

### 2.3 内存管理策略
- 核心库自身采用静态内存池，预分配固定大小的缓冲区，不进行动态内存分配。
- 提供内存分配器接口(`lbrpc_allocator_t`)，允许用户注入自定义内存分配策略。
- 如果用户提供自定义内存分配器，框架会使用它处理动态分配需求，否则仅使用预分配资源。
- 所有接口设计优先基于栈分配或预分配缓冲区。
- 明确记录每个函数的内存分配行为，便于用户了解内存使用情况。

## 3. 关键接口设计

### 3.1 核心数据结构
```c
// 改进的地址结构设计
typedef struct { 
    uint16_t transport_index;  // lbrpc_context_t中transport的索引
    uint16_t addr_len;         // 地址数据长度
    uint8_t addr_data[64];     // 内嵌地址数据，避免指针生命周期问题
} lbrpc_address_t;

// 地址创建辅助函数
static inline lbrpc_address_t lbrpc_address_create_driver_channel(uint16_t transport_index, 
                                                                 uint32_t channel_id, uint32_t node_id) {
    lbrpc_address_t addr = {0};
    addr.transport_index = transport_index;
    addr.addr_len = sizeof(driver_channel_addr_t);
    
    driver_channel_addr_t* channel_addr = (driver_channel_addr_t*)addr.addr_data;
    channel_addr->channel_id = channel_id;
    channel_addr->node_id = node_id;
    
    return addr;
}

static inline lbrpc_address_t lbrpc_address_create_nng_bus(uint16_t transport_index,
                                                          uint64_t node_id, const char* url) {
    lbrpc_address_t addr = {0};
    addr.transport_index = transport_index;
    addr.addr_len = sizeof(nng_bus_addr_t);
    
    nng_bus_addr_t* nng_addr = (nng_bus_addr_t*)addr.addr_data;
    nng_addr->node_id = node_id;
    strncpy(nng_addr->url, url, sizeof(nng_addr->url) - 1);
    
    return addr;
}

// 传输层特定的地址结构定义
// 驱动通道地址结构
typedef struct {
    uint32_t channel_id;      // 通道ID
    uint32_t node_id;         // 节点ID（0xFFFFFFFF表示广播）
} driver_channel_addr_t;

// nng bus 地址结构
typedef struct {
    uint64_t node_id;         // 节点ID（0xFFFFFFFFFFFFFFFF表示广播）
    char url[64];             // nng URL
} nng_bus_addr_t;

// 消息结构
typedef struct {
    uint32_t method_id;     // 方法 ID
    uint16_t msg_id;        // 消息 ID
    uint8_t* payload;       // 消息负载
    size_t payload_len;     // 负载长度
    lbrpc_address_t src_addr;  // 源地址
    lbrpc_address_t dest_addr; // 目标地址
} lbrpc_msg_t;

// 事件回调函数类型（抽象化，不暴露 libev 依赖）
typedef void (*lbrpc_io_event_callback_t)(int fd, int events, void* user_data);
typedef void (*lbrpc_timer_callback_t)(void* user_data);
typedef void (*lbrpc_async_task_callback_t)(void* user_data);

// I/O 事件类型定义
#define LBRPC_EVENT_READ    0x01
#define LBRPC_EVENT_WRITE   0x02
#define LBRPC_EVENT_ERROR   0x04

// 内存分配器接口
typedef struct {
    void* (*alloc)(size_t size);
    void (*free)(void* ptr);
    void* (*realloc)(void* ptr, size_t new_size);
} lbrpc_allocator_t;

// 内存配置结构
typedef struct {
    size_t max_pending_requests;    // 最大待处理请求数
    size_t max_message_size;        // 最大消息大小
    size_t receive_buffer_size;     // 接收缓冲区大小
    size_t send_buffer_size;        // 发送缓冲区大小
    size_t max_codec_entries;       // 最大编解码器条目数
} lbrpc_memory_config_t;

// 默认内存配置
#define LBRPC_DEFAULT_MEMORY_CONFIG { \
    .max_pending_requests = 32, \
    .max_message_size = 4096, \
    .receive_buffer_size = 8192, \
    .send_buffer_size = 8192, \
    .max_codec_entries = 64 \
} 
```

### 3.2 传输与组包接口
```c
// 传输层接口（不暴露 libev 依赖）
typedef struct {
    uint8_t transport_type;     // 传输层类型，用于标识传输层
    int (*send)(const lbrpc_address_t* dest_addr, const uint8_t* data, size_t len, void* user_data);
    int (*recv)(lbrpc_address_t* source_addr, uint8_t* buf, size_t buf_len, size_t* out_len, void* user_data);
    
    // 事件驱动接口（抽象化）
    int (*get_fd)(void* user_data);  // 获取文件描述符用于事件监听
    int (*setup_events)(void* user_data); // 设置事件监听（内部会注册到事件系统）
    void (*cleanup_events)(void* user_data); // 清理事件监听
    
    // 异步接收接口（可选，如果不支持可设为 NULL）
    int (*async_recv)(lbrpc_address_t* source_addr, void (*callback)(uint8_t* buf, size_t len, void* user_data), 
                      uint32_t timeout_ms, void* user_data);
    void* user_data;  // 用户自定义数据
} lbrpc_transport_t;

// 组包器接口
typedef struct {
    size_t (*pack)(const lbrpc_msg_t* msg, uint8_t* out_buf, size_t out_buf_len, void* user_data);
    int (*unpack)(const uint8_t* in_buf, size_t in_buf_len, lbrpc_msg_t* out_msg, void* user_data);
    void* user_data;  // 用户自定义数据
} lbrpc_packetizer_t;

// 事件系统配置（隐藏 libev 实现细节）
typedef struct {
    size_t max_io_watchers;     // 最大 I/O 监听器数量
    size_t max_timers;          // 最大定时器数量
    uint32_t event_flags;       // 事件系统标志（内部映射到 libev 标志）
} lbrpc_event_config_t;
```

### 3.3 Request/Response 模式接口
```c
// 请求处理回调函数
typedef int (*lbrpc_request_handler_t)(const lbrpc_msg_t* request, lbrpc_msg_t* response, void* user_data);

// 响应回调函数（支持异步处理）
typedef int (*lbrpc_response_handler_t)(const lbrpc_msg_t* response, int error_code, void* user_data);

// 超时回调函数
typedef void (*lbrpc_timeout_handler_t)(uint16_t msg_id, void* user_data);

// 请求上下文（用于异步处理和超时管理）
typedef struct {
    uint16_t msg_id;
    lbrpc_response_handler_t callback;
    lbrpc_timeout_handler_t timeout_callback;
    void* timer_handle;           // 抽象的定时器句柄（内部映射到 libev 定时器）
    void* user_data;
    uint64_t timestamp;           // 请求发送时间
} lbrpc_request_ctx_t;

// Request/Response 模式相关接口
int lbrpc_reqresp_register_handler(lbrpc_context_t* ctx, uint32_t method_id, 
                                  lbrpc_request_handler_t handler, void* user_data);
int lbrpc_reqresp_send_request(lbrpc_context_t* ctx, const lbrpc_address_t* dest_addr, 
                              const lbrpc_msg_t* request, lbrpc_response_handler_t callback, 
                              uint32_t timeout_ms, void* user_data);
int lbrpc_reqresp_send_request_async(lbrpc_context_t* ctx, const lbrpc_address_t* dest_addr, 
                                    const lbrpc_msg_t* request, lbrpc_response_handler_t callback,
                                    lbrpc_timeout_handler_t timeout_callback,
                                    uint32_t timeout_ms, void* user_data);
int lbrpc_reqresp_send_response(lbrpc_context_t* ctx, const lbrpc_address_t* dest_addr, 
                               const lbrpc_msg_t* response);
```

### 3.4 Notify 模式接口
```c
// 通知处理回调函数
typedef int (*lbrpc_notify_handler_t)(const lbrpc_msg_t* notification, void* user_data);

// Notify 模式相关接口
int lbrpc_notify_register_handler(lbrpc_context_t* ctx, uint32_t method_id, 
                                 lbrpc_notify_handler_t handler, void* user_data);
int lbrpc_notify_send(lbrpc_context_t* ctx, const lbrpc_address_t* dest_addr, 
                     const lbrpc_msg_t* notification);
```

### 3.5 编解码器注册接口
```c
// 通用编解码器接口（核心库不实现具体的序列化逻辑）
typedef struct {
    size_t (*serialize)(const void* struct_ptr, uint8_t* out_buf, size_t out_buf_len);
    int (*deserialize)(const uint8_t* in_buf, size_t in_buf_len, void* struct_ptr, size_t struct_size);
    size_t struct_size;  // 结构体大小
    const char* type_name; // 类型名称（可选，用于调试）
} lbrpc_codec_t;

// 编解码器注册接口
int lbrpc_codec_register(lbrpc_context_t* ctx, uint32_t type_id, const lbrpc_codec_t* codec);
int lbrpc_codec_unregister(lbrpc_context_t* ctx, uint32_t type_id);
const lbrpc_codec_t* lbrpc_codec_get(lbrpc_context_t* ctx, uint32_t type_id);

// 编解码器注册中心内部结构
typedef struct {
    uint32_t type_id;
    const lbrpc_codec_t* codec;
} lbrpc_codec_entry_t;

typedef struct {
    lbrpc_codec_entry_t* entries;
    size_t max_entries;
    size_t entry_count;
} lbrpc_codec_registry_t;
```

### 3.6 主要 API 接口
```c
// 完善的上下文结构
typedef struct {
    const lbrpc_transport_t** transports;  // 传输层数组，支持多种传输层
    size_t transport_count;                // 传输层数量
    const lbrpc_packetizer_t* packetizer;
    const lbrpc_allocator_t* allocator;    // Optional, NULL 表示使用默认静态内存池
    lbrpc_memory_config_t memory_config;   // 内存配置
    
    // 事件系统（隐藏内部实现）
    void* event_context;                   // 内部事件上下文（指向 libev 相关结构）
    bool use_external_event_loop;          // 是否使用外部事件循环
    
    // 异步请求管理
    lbrpc_request_ctx_t* pending_requests; // 待处理请求数组
    size_t max_pending_requests;           // 最大待处理请求数
    
    // 编解码器注册中心
    lbrpc_codec_registry_t codec_registry;
    
    // 错误状态
    lbrpc_error_t last_error;
    
    // 预分配缓冲区
    uint8_t* receive_buffer;
    uint8_t* send_buffer;
    
    void* internal_state;                  // 内部状态，用户不应直接访问
} lbrpc_context_t;

// 方法描述结构
typedef struct {
    uint32_t method_id;        // 方法 ID
    uint8_t mode;              // 方法模式：REQUEST_RESPONSE 或 NOTIFY
    uint8_t payload_type;      // 负载类型：RAW_BINARY 或 CAPNPROTO
    uint32_t capnp_type_id;    // Capnproto 类型 ID（仅当 payload_type 为 CAPNPROTO 时有效）
} lbrpc_method_t;

// 完善的错误码定义
typedef enum {
    LBRPC_OK = 0,
    LBRPC_ERR_TIMEOUT = -1,
    LBRPC_ERR_NOT_FOUND = -2,
    LBRPC_ERR_INVALID_PARAM = -3,
    LBRPC_ERR_NO_MEMORY = -4,
    LBRPC_ERR_TRANSPORT = -5,
    LBRPC_ERR_SERIALIZE = -6,
    LBRPC_ERR_EVENT_LOOP = -7,
    LBRPC_ERR_ASYNC_FAILED = -8,
    LBRPC_ERR_BUFFER_TOO_SMALL = -9,
    LBRPC_ERR_CODEC_NOT_FOUND = -10,
    LBRPC_ERR_INVALID_MESSAGE = -11,
    LBRPC_ERR_TRANSPORT_CLOSED = -12,
    LBRPC_ERR_UNKNOWN = -99
} lbrpc_error_t;

// 错误信息获取接口
const char* lbrpc_error_string(lbrpc_error_t error);
lbrpc_error_t lbrpc_get_last_error(lbrpc_context_t* ctx);
void lbrpc_set_last_error(lbrpc_context_t* ctx, lbrpc_error_t error);

// 方法模式枚举
typedef enum {
    LBRPC_METHOD_MODE_REQUEST_RESPONSE = 0, // 请求/响应模式
    LBRPC_METHOD_MODE_NOTIFY = 1           // 通知模式（无响应）
} lbrpc_method_mode_t;

// 负载类型枚举
typedef enum {
    LBRPC_PAYLOAD_RAW_BINARY = 0,
    LBRPC_PAYLOAD_CAPNPROTO = 1
} lbrpc_payload_type_t;

// 事件循环配置（抽象化）
typedef struct {
    bool use_external_loop;     // 是否使用外部提供的事件循环
    void* external_loop_handle; // 外部事件循环句柄（如果 use_external_loop 为 true）
    uint32_t max_io_watchers;   // 最大 I/O 监听器数量
    uint32_t max_timers;        // 最大定时器数量
    uint32_t event_flags;       // 事件系统标志
} lbrpc_event_loop_config_t;

// 核心 API（隐藏 libev 实现细节）
int lbrpc_init(const lbrpc_transport_t** transports, size_t transport_count,
               const lbrpc_packetizer_t* packetizer, const lbrpc_allocator_t* allocator, 
               const lbrpc_event_loop_config_t* event_config, lbrpc_context_t** ctx);
int lbrpc_deinit(lbrpc_context_t** ctx);

// 事件循环控制
int lbrpc_run_event_loop(lbrpc_context_t* ctx, uint32_t flags);
int lbrpc_stop_event_loop(lbrpc_context_t* ctx);
int lbrpc_run_event_loop_once(lbrpc_context_t* ctx, uint32_t timeout_ms);

// 核心消息处理接口
int lbrpc_handle_received_data(lbrpc_context_t* ctx, const lbrpc_address_t* source_addr, 
                              const uint8_t* data, size_t len);
int lbrpc_register_io_event(lbrpc_context_t* ctx, int fd, int events, 
                           lbrpc_io_event_callback_t callback, void* user_data);
int lbrpc_unregister_io_event(lbrpc_context_t* ctx, int fd);

// 定时器接口
int lbrpc_timer_start(lbrpc_context_t* ctx, uint32_t timeout_ms, bool repeat,
                     lbrpc_timer_callback_t callback, void* user_data, void** timer_handle);
int lbrpc_timer_stop(lbrpc_context_t* ctx, void* timer_handle);

// 异步任务接口
int lbrpc_async_task_post(lbrpc_context_t* ctx, lbrpc_async_task_callback_t callback, void* user_data);
```

## 4. capnproto 插件式代码生成器设计

### 4.1 插件架构概述
LiteBusRpc 的代码生成器采用 capnproto 编译器插件（plugin）架构，而不是独立的代码生成工具。这种设计具有以下优势：
- **标准集成**：完全集成到 capnproto 官方工具链中
- **扩展性强**：在 capnproto 原有结构体生成基础上，增加 RPC interface 代码生成
- **维护简单**：跟随 capnproto 版本演进，自动获得新特性支持
- **工具链统一**：用户使用标准的 `capnp compile` 命令即可完成所有代码生成

### 4.2 插件实现方式
```bash
# 安装插件
cargo install lbrpc-capnp-plugin

# 使用标准 capnproto 编译器调用插件
capnp compile --src-prefix=schemas/ \
    --plugin=lbrpc-cpp=/path/to/lbrpc-capnp-plugin \
    --plugin=lbrpc-rust=/path/to/lbrpc-capnp-plugin \
    --lbrpc-cpp_out=./generated/cpp/ \
    --lbrpc-rust_out=./generated/rust/ \
    schemas/service.capnp
```

### 4.3 支持的目标语言
- **C++**：生成 C++11 兼容代码，提供面向对象的接口封装
- **Rust**：生成 no_std Rust 代码，用于嵌入式环境，支持异步操作

### 4.4 代码生成分层
1. **capnproto 原生层**：由 capnproto 编译器自动生成
   - 基础结构体定义
   - Reader/Builder 类
   - 序列化/反序列化实现
   
2. **LiteBusRpc 扩展层**：由插件额外生成
   - RPC 客户端/服务端接口
   - 消息分发路由代码
   - 编解码器注册代码
   - 异步调用包装器

### 4.5 插件代码生成流程
```
capnp schema → capnp compile → plugin → 
    ├── 解析 interface 定义
    ├── 解析 lbrpc 注解
    ├── 生成 RPC 客户端代码
    ├── 生成 RPC 服务端代码
    ├── 生成编解码器注册代码
    └── 生成类型 ID 映射表
```

### 4.6 核心库与生成代码的关系
- **核心库职责**：提供通用的 RPC 调度、事件处理、传输层抽象
- **生成代码职责**：提供具体的序列化、反序列化、类型注册实现
- **接口边界**：通过 `lbrpc_codec_t` 接口进行解耦

### 4.7 多语言互操作
- 所有生成的代码均基于相同的二进制协议
- 支持 C++ 与 Rust 间的跨语言 RPC 调用
- 统一的错误处理机制
- C++ 代码提供 C 接口兼容层，便于与 C 核心库集成

## 5. 通信模式详解

### 5.1 Request/Response 模式
- **用途**：需要响应的双向通信
- **单播**：客户端向特定服务端发送请求，等待响应
- **广播**：客户端向所有服务端发送请求，可能收到多个响应
- **超时处理**：支持请求超时机制
- **错误处理**：支持详细的错误码返回

### 5.2 Notify 模式
- **用途**：单向通知，无需响应
- **单播**：向特定接收方发送通知
- **广播**：向所有接收方发送通知
- **性能优化**：无需等待响应，延迟更低
- **事件驱动**：适合状态更新、事件通知等场景

### 5.3 地址结构设计与路由

#### 5.3.1 地址结构优势
新的 `lbrpc_address_t` 结构采用索引+指针的设计，具有以下优势：

1. **灵活性更强**：支持任意大小的地址结构，不限制为固定字节数
2. **类型安全**：通过具体的地址结构类型提供编译时类型检查
3. **性能优化**：直接通过索引访问传输层，O(1) 查找时间
4. **扩展性好**：易于添加新的传输层和地址类型

#### 5.3.2 路由逻辑
框架在发送消息时的路由逻辑：

```c
int lbrpc_send_message(lbrpc_context_t* ctx, const lbrpc_address_t* dest_addr, 
                      const uint8_t* data, size_t len) {
    // 1. 检查传输层索引是否有效
    if (dest_addr->transport_index >= ctx->transport_count) {
        return LBRPC_ERR_INVALID_PARAM;
    }
    
    // 2. 直接通过索引获取传输层
    const lbrpc_transport_t* transport = ctx->transports[dest_addr->transport_index];
    
    // 3. 调用传输层的发送函数
    return transport->send(dest_addr, data, len, transport->user_data);
}
```

### 5.4 传输层地址示例
```c
// 驱动通道地址结构定义
typedef struct {
    uint32_t channel_id;      // 通道ID
    uint32_t node_id;         // 节点ID（0xFFFFFFFF表示广播）
} driver_channel_addr_t;

// nng bus 地址结构定义
typedef struct {
    uint64_t node_id;         // 节点ID（0xFFFFFFFFFFFFFFFF表示广播）
    char url[64];             // nng URL
} nng_bus_addr_t;

// 驱动通道传输层地址示例（假设索引0为驱动通道传输层）
driver_channel_addr_t driver_unicast_addr = {
    .channel_id = 0x01,
    .node_id = 0x05
};
lbrpc_address_t driver_unicast = {
    .transport_index = 0,     // 指向 lbrpc_context_t.transports[0]
    .addr = &driver_unicast_addr
};

// 广播地址示例
driver_channel_addr_t driver_broadcast_addr = {
    .channel_id = 0x01,
    .node_id = 0xFFFFFFFF     // 广播标识
};
lbrpc_address_t driver_broadcast = {
    .transport_index = 0,     // 指向 lbrpc_context_t.transports[0]
    .addr = &driver_broadcast_addr
};

// nng bus 模式地址示例（假设索引1为nng传输层）
nng_bus_addr_t nng_unicast_addr = {
    .node_id = 0x123456789ABCDEF0,
    .url = "tcp://*************:5555"
};
lbrpc_address_t nng_unicast = {
    .transport_index = 1,     // 指向 lbrpc_context_t.transports[1]
    .addr = &nng_unicast_addr
};

// nng 广播地址示例
nng_bus_addr_t nng_broadcast_addr = {
    .node_id = 0xFFFFFFFFFFFFFFFF,  // 广播标识
    .url = "*"                      // 广播到所有连接
};
lbrpc_address_t nng_broadcast = {
    .transport_index = 1,     // 指向 lbrpc_context_t.transports[1]
    .addr = &nng_broadcast_addr
};
```

### 5.3.3 地址使用示例

```c
// 创建驱动通道地址
driver_channel_addr_t my_channel_addr = {
    .channel_id = 1,
    .node_id = 0x123
};

lbrpc_address_t dest_addr = {
    .transport_index = 0,      // 指向第一个传输层
    .addr = &my_channel_addr
};

// 发送消息
lbrpc_reqresp_send_request(ctx, &dest_addr, &request, callback, 1000, user_data);
```

## 7. 核心接口与事件系统集成

### 7.1 事件系统抽象接口

框架提供抽象的事件系统接口，隐藏 libev 实现细节：

```c
// 事件注册接口
int lbrpc_register_io_event(lbrpc_context_t* ctx, int fd, int events, 
                           lbrpc_io_event_callback_t callback, void* user_data);
int lbrpc_unregister_io_event(lbrpc_context_t* ctx, int fd);

// 消息处理接口
int lbrpc_handle_received_data(lbrpc_context_t* ctx, const lbrpc_address_t* source_addr, 
                              const uint8_t* data, size_t len);

// 传输层索引管理
int lbrpc_get_transport_index(lbrpc_context_t* ctx, uint8_t transport_type);
const lbrpc_transport_t* lbrpc_get_transport(lbrpc_context_t* ctx, uint16_t transport_index);
```

### 7.2 完整的传输层接口使用示例

```c
// 完整的传输层初始化和使用示例
int example_transport_usage() {
    // 1. 初始化传输层
    driver_channel_transport_t* driver_transport = malloc(sizeof(driver_channel_transport_t));
    driver_channel_transport_init(driver_transport, channel_fd, 1, 0x123, rpc_ctx);
    
    // 2. 创建传输层数组
    const lbrpc_transport_t* transports[] = {
        &driver_transport->base
    };
    
    // 3. 初始化 RPC 上下文
    lbrpc_context_t* ctx;
    lbrpc_event_loop_config_t event_config = {
        .use_external_loop = false,
        .max_io_watchers = 32,
        .max_timers = 16,
        .event_flags = 0
    };
    
    int ret = lbrpc_init(transports, 1, &default_packetizer, NULL, &event_config, &ctx);
    if (ret != LBRPC_OK) {
        return ret;
    }
    
    // 4. 注册方法处理器
    lbrpc_reqresp_register_handler(ctx, 0x1001, my_request_handler, NULL);
    lbrpc_notify_register_handler(ctx, 0x2001, my_notify_handler, NULL);
    
    // 5. 运行事件循环
    return lbrpc_run_event_loop(ctx, 0);
}
```

### 7.3 地址结构使用最佳实践

**地址生命周期管理**：
- 地址结构必须在消息发送期间保持有效
- 对于静态地址，可以使用全局变量或静态变量
- 对于动态地址，需要确保在异步操作完成前不被释放

**传输层索引管理**：
- 传输层索引在初始化时确定，运行时不可修改
- 建议使用枚举定义传输层索引，避免硬编码

```c
// 推荐的地址使用模式
typedef enum {
    TRANSPORT_DRIVER_CHANNEL = 0,
    TRANSPORT_NNG_BUS = 1,
    TRANSPORT_MAX
} transport_index_t;

// 创建单播地址
static driver_channel_addr_t create_unicast_addr(uint32_t channel_id, uint32_t node_id) {
    return (driver_channel_addr_t){
        .channel_id = channel_id,
        .node_id = node_id
    };
}

// 使用地址发送消息
void send_to_node(lbrpc_context_t* ctx, uint32_t node_id, const lbrpc_msg_t* msg) {
    driver_channel_addr_t addr = create_unicast_addr(1, node_id);
    lbrpc_address_t dest_addr = {
        .transport_index = TRANSPORT_DRIVER_CHANNEL,
        .addr = &addr
    };
    
    lbrpc_reqresp_send_request(ctx, &dest_addr, msg, response_callback, 5000, NULL);
}
```

## 8. 典型用例
- **单播通信**：设备间点对点的请求/响应或通知
- **广播通信**：一对多的状态更新、事件通知
- **混合场景**：同时支持单播命令控制和广播状态同步
- **驱动通道传输**：通过设备驱动提供的读写通道进行通信（UART、SPI、CAN等）
- **nng bus 网络传输**：通过 nng 库的 bus 模式实现网络通信（仅 Rust）

### 8.5 传输层实现指南

### 8.5.1 传输层设计原则
每个传输层实现需要：
1. **设置传输层类型**：`transport_type` 字段用于标识传输层
2. **地址解析**：根据 `lbrpc_address_t.addr` 指向的具体地址结构判断单播/广播
3. **物理层适配**：将逻辑地址映射到具体的物理传输
4. **错误处理**：返回标准的错误码

### 8.5.2 驱动通道传输层实现示例（抽象事件接口）
```c
// 驱动通道传输层实现
typedef struct {
    lbrpc_transport_t base;
    int channel_fd;           // 驱动通道文件描述符
    uint32_t channel_id;      // 通道ID
    uint32_t local_node_id;   // 本地节点ID
    lbrpc_context_t* rpc_ctx; // RPC 上下文引用
    bool events_setup;        // 事件是否已设置
} driver_channel_transport_t;

// 传输层事件处理函数（由框架调用）
void driver_channel_handle_io_event(int fd, int events, void* user_data) {
    driver_channel_transport_t* transport = (driver_channel_transport_t*)user_data;
    
    if (events & LBRPC_EVENT_READ) {
        // 处理接收数据
        uint8_t buffer[1024];
        lbrpc_address_t source_addr;
        size_t recv_len;
        
        int ret = driver_channel_recv(&source_addr, buffer, sizeof(buffer), &recv_len, transport);
        if (ret == LBRPC_OK && recv_len > 0) {
            // 将接收到的数据交给 RPC 层处理
            lbrpc_handle_received_data(transport->rpc_ctx, &source_addr, buffer, recv_len);
        }
    }
    
    if (events & LBRPC_EVENT_ERROR) {
        // 处理错误
        printf("Driver channel I/O error\n");
    }
}

int driver_channel_setup_events(void* user_data) {
    driver_channel_transport_t* transport = (driver_channel_transport_t*)user_data;
    
    if (!transport->events_setup) {
        // 注册 I/O 事件到框架的事件系统
        int ret = lbrpc_register_io_event(transport->rpc_ctx, transport->channel_fd, 
                                         LBRPC_EVENT_READ, driver_channel_handle_io_event, transport);
        if (ret == LBRPC_OK) {
            transport->events_setup = true;
        }
        return ret;
    }
    
    return LBRPC_OK;
}

void driver_channel_cleanup_events(void* user_data) {
    driver_channel_transport_t* transport = (driver_channel_transport_t*)user_data;
    
    if (transport->events_setup) {
        lbrpc_unregister_io_event(transport->rpc_ctx, transport->channel_fd);
        transport->events_setup = false;
    }
}

int driver_channel_get_fd(void* user_data) {
    driver_channel_transport_t* transport = (driver_channel_transport_t*)user_data;
    return transport->channel_fd;
}

int driver_channel_send(const lbrpc_address_t* dest_addr, const uint8_t* data, size_t len, void* user_data) {
    driver_channel_transport_t* transport = (driver_channel_transport_t*)user_data;
    
    // 检查地址指针是否有效
    if (!dest_addr->addr) {
        return LBRPC_ERR_INVALID_PARAM;
    }
    
    // 解析地址结构
    driver_channel_addr_t* channel_addr = (driver_channel_addr_t*)dest_addr->addr;
    
    // 检查通道ID是否匹配
    if (channel_addr->channel_id != transport->channel_id) {
        return LBRPC_ERR_INVALID_PARAM;
    }
    
    if (channel_addr->node_id == 0xFFFFFFFF) {
        // 广播：发送到所有节点
        return driver_channel_broadcast_send(transport->channel_fd, data, len);
    } else {
        // 单播：发送到特定节点
        return driver_channel_unicast_send(transport->channel_fd, channel_addr->node_id, data, len);
    }
}

int driver_channel_recv(lbrpc_address_t* source_addr, uint8_t* buf, size_t buf_len, size_t* out_len, void* user_data) {
    driver_channel_transport_t* transport = (driver_channel_transport_t*)user_data;
    
    uint32_t sender_node_id;
    int ret = driver_channel_receive(transport->channel_fd, &sender_node_id, buf, buf_len, out_len);
    if (ret == LBRPC_OK) {
        // 分配并填充源地址结构
        static driver_channel_addr_t source_channel_addr;  // 使用静态变量避免动态分配
        source_channel_addr.channel_id = transport->channel_id;
        source_channel_addr.node_id = sender_node_id;
        
        // 填充地址（传输层索引由调用方设置）
        source_addr->addr = &source_channel_addr;
    }
    
    return ret;
}

void driver_channel_transport_init(driver_channel_transport_t* transport, int channel_fd, 
                                  uint32_t channel_id, uint32_t node_id, lbrpc_context_t* rpc_ctx) {
    transport->base.transport_type = 0;  // 传输层类型标识（可选）
    transport->base.send = driver_channel_send;
    transport->base.recv = driver_channel_recv;
    transport->base.get_fd = driver_channel_get_fd;
    transport->base.setup_events = driver_channel_setup_events;
    transport->base.cleanup_events = driver_channel_cleanup_events;
    transport->base.async_recv = NULL;  // 使用事件驱动，不需要轮询
    transport->base.user_data = transport;
    
    transport->channel_fd = channel_fd;
    transport->channel_id = channel_id;
    transport->local_node_id = node_id;
    transport->rpc_ctx = rpc_ctx;
    transport->events_setup = false;
}
```

### 8.5.3 nng bus 模式传输层设计（仅 Rust 实现）

nng bus 传输层设计说明：

**设计原则**：
- 使用 nng 库的 bus 模式实现多节点通信
- 通过 eventfd（Linux）或 pipe（跨平台）与 libev 集成
- 后台线程处理接收，避免阻塞主事件循环
- 地址结构使用统一的 `lbrpc_address_t` 设计

**关键组件**：
1. **NngBusTransport 结构体**：封装 nng socket 和状态管理
2. **后台接收线程**：异步接收消息并通知主线程
3. **事件通知机制**：使用文件描述符通知 libev 有新消息
4. **C FFI 接口**：与 C 核心库的互操作接口

**地址结构示例**：
```c
// nng bus 地址结构
typedef struct {
    uint64_t node_id;         // 节点ID（0xFFFFFFFFFFFFFFFF表示广播）
    char url[64];             // nng URL
} nng_bus_addr_t;

// 使用示例
nng_bus_addr_t nng_addr = {
    .node_id = 0x123456789ABCDEF0,
    .url = "tcp://*************:5555"
};

lbrpc_address_t dest_addr = {
    .transport_index = 1,     // nng 传输层索引
    .addr = &nng_addr
};
```

**消息格式**：
- 单播消息：[发送方节点ID(8字节)] + [目标节点ID(8字节)] + [payload]
- 广播消息：[发送方节点ID(8字节)] + [payload]

**实现要点**：
- 使用 Rust 实现传输层逻辑，通过 C FFI 接口与核心库集成
- 支持异步消息处理和事件驱动
- 提供完整的 C 兼容接口
- 处理连接管理和错误恢复

## 9. 架构设计要点总结

### 9.1 序列化处理的分离设计

LiteBusRpc 采用了明确的职责分离设计：

**核心库职责（纯 C 实现）**：
- 消息路由和分发
- 事件驱动调度
- 传输层抽象
- 超时管理
- 内存管理
- **不处理具体的序列化逻辑**

**代码生成器职责（capnproto 插件）**：
- 解析 capnproto schema
- 生成结构体定义
- 生成序列化/反序列化实现
- 生成编解码器注册代码
- 生成 RPC 客户端/服务端接口

**接口边界**：
- 通过 `lbrpc_codec_t` 接口实现解耦
- 核心库提供编解码器注册中心（`lbrpc_codec_registry`）
- 生成的代码在初始化时自动注册编解码器

### 9.2 插件架构的优势

**1. 标准化集成**
- 基于 capnproto 官方插件机制
- 使用标准的 `capnp compile` 命令
- 与现有 capnproto 工具链无缝集成

**2. 代码生成分层**
- capnproto 原生层：处理基础序列化
- LiteBusRpc 扩展层：提供 RPC 接口
- 避免重复实现序列化逻辑

**3. 维护性强**
- 跟随 capnproto 版本演进
- 自动获得性能优化和新特性
- 减少维护工作量

### 9.3 多语言支持策略

**C++ 代码特点**：
- 提供现代 C++ 接口（C++11+）
- 内部使用 capnproto 原生序列化
- 提供 C 兼容层用于与核心库集成
- 自动编解码器注册

**Rust 代码特点**：
- 支持 no_std 环境
- 异步操作支持
- 类型安全的接口设计
- 与 nng 传输层的原生集成

### 9.4 扩展性考虑

这种设计使得 LiteBusRpc 可以轻松支持：
- 其他序列化协议（如 protobuf、MessagePack）
- 更多目标语言（Python、Go、JavaScript）
- 自定义序列化实现
- 第三方编解码器插件

## 10. 实现指引

### 10.1 开发顺序建议

1. **核心库开发**（第一阶段）
   - 实现事件抽象层和 libev 适配
   - 实现核心数据结构和基础 API
   - 实现请求/响应和通知模式
   - 实现传输层抽象接口

2. **传输层实现**（第二阶段）
   - 实现驱动通道传输层（C 语言）
   - 实现 nng bus 传输层（Rust 语言）
   - 编写传输层测试用例

3. **代码生成器**（第三阶段）
   - 实现 capnproto 插件框架
   - 实现 C++ 代码生成
   - 实现 Rust 代码生成
   - 集成测试和文档

### 10.2 关键实现注意事项

**内存管理**：
- 严格避免动态内存分配
- 使用静态内存池和预分配缓冲区
- 提供内存分配器接口用于定制

**线程安全**：
- 核心库设计为单线程使用
- 传输层需要处理多线程安全（如 nng Rust 实现）
- 使用适当的同步机制保护共享资源

**错误处理**：
- 统一的错误码定义
- 详细的错误信息记录
- 优雅的错误恢复机制

**性能优化**：
- 零拷贝数据传输
- 高效的消息路由
- 最小化系统调用

### 10.3 测试策略

**单元测试**：
- 核心数据结构测试
- 事件系统抽象层测试
- 序列化/反序列化测试

**集成测试**：
- 端到端 RPC 调用测试
- 多传输层混合测试
- 错误场景和边界条件测试

**性能测试**：
- 延迟和吞吐量基准测试
- 内存使用情况分析
- 长时间运行稳定性测试
