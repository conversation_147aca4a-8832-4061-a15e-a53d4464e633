# 混元架构平台主控软件概要设计文档

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档名称 | 混元架构平台主控软件概要设计文档 |
| 文档版本 | V0.1 |
| 创建日期 | 2025-07-22 |
| 最后修改 | 2025-07.29 |
| 文档状态 | 草稿 |
| 密级 | 内部 |

## 引言

### 编写目的

本文档旨在详细描述混元架构平台主控板卡应用软件的概要设计，包括系统架构、功能模块、接口规范、数据设计等关键技术要素。通过规范化的设计描述，为后续的详细设计、开发实现、系统集成和测试验证提供技术依据，确保软件系统能够满足功能需求、性能指标和质量要求。

### 适用范围

本设计文档适用于：
- 混元系统主控软件的开发团队
- 系统架构师和技术负责人
- 软件测试和质量保证人员
- 项目管理和技术支持人员
- 需要了解系统设计细节的相关干系人

### 文档范围

本文档覆盖混元架构平台主控板卡软件的：
- 系统总体架构设计
- 功能模块划分和接口定义
- 数据库设计和数据流
- 关键技术方案和实现策略
- 性能指标和约束条件
- 可靠性和安全性设计

### 术语和缩略语

| 术语/缩略语 | 英文全称 | 中文解释 |
|-------------|----------|----------|
| 混元主机 | Hyun Matrix | 混元架构平台的KVM信号交换矩阵设备，主要由背板、I/O板卡和主控板卡组成 |
| 背板 | Backplane | 主机中的核心组件，负责连接各个板卡并交换数据，提供高速的数据通道 |
| 主控板卡 | Master Control Board | 插入主机卡槽内，负责系统的管理和调度的控制板卡 |
| I/O板卡 | Input/Output Board | 插入主机卡槽内，实现KVM信号输入/输出功能的板卡 |
| 级联板卡 | Cascade Board | 插入主机卡槽内，实现混元主机间级联连接的板卡 |
| TX外设 | Transmitter Device | 混元架构平台中的接入端，与信号源连接的发送设备 |
| RX外设 | Receiver Device | 混元架构平台中的管控端，与显示屏幕连接的接收设备 |
| KVM | Keyboard Video Mouse | 键盘、视频、鼠标信号的集成传输技术 |
| MCU | Microcontroller Unit | 微控制器单元 |
| FPGA | Field Programmable Gate Array | 现场可编程门阵列 |
| SNMP | Simple Network Management Protocol | 简单网络管理协议 |
| REST | Representational State Transfer | 表现层状态转移，一种软件架构风格 |
| ORM | Object Relational Mapping | 对象关系映射 |
| API | Application Programming Interface | 应用程序编程接口 |
| TCP | Transmission Control Protocol | 传输控制协议 |
| UDP | User Datagram Protocol | 用户数据报协议 |
| HTTP/HTTPS | HyperText Transfer Protocol (Secure) | 超文本传输协议（安全版） |
| JSON | JavaScript Object Notation | JavaScript对象表示法，一种数据交换格式 |
| SQL | Structured Query Language | 结构化查询语言 |
| eMMC | embedded MultiMediaCard | 嵌入式多媒体卡 |
| LPDDR | Low Power Double Data Rate | 低功耗双倍数据速率内存 |

### 参考资料

| 序号 | 文档名称 | 版本 | 发布日期 | 说明 |
|------|----------|------|----------|------|
| [1] | 《基于混元架构平台的设计与研究产品需求规格说明书》 | V1.0 | 2025-02-05 | 产品功能需求规范 |

## 系统概述

### 系统背景

混元架构平台是新一代KVM矩阵系统，支持大规模KVM信号的接入、交换和分发。主控板卡作为整个系统的"大脑"，承担着系统管理、资源调度、业务控制等核心职责。

### 系统定位

主控软件是混元架构平台的核心控制组件，具有以下特点：
- **嵌入式实时系统**：运行在资源受限的嵌入式硬件平台上
- **高可靠性要求**：支持主备冗余，故障自动切换
- **业务关键系统**：承载KVM业务的核心控制逻辑

### 主要功能概述

主控软件实现以下核心功能：

1. **系统管理功能**
   - 硬件资源管理和监控
   - 系统配置和参数管理
   - 固件升级和系统维护

2. **业务控制功能**
   - KVM信号路由控制
   - 坐席和大屏业务管理
   - 级联业务管理

3. **通信管理功能**
   - 板卡间通信协调
   - 外部接口服务提供
   - 网络通信和协议处理

4. **可靠性保障功能**
   - 主备冗余和故障切换
   - 系统状态监控和告警
   - 数据备份和恢复

### 关键技术指标

| 指标类别 | 指标项 | 指标值 | 说明 |
|----------|--------|--------|------|
| 性能指标 | 信号切换延迟 | ≤100ms | KVM信号切换响应时间 |
| | CPU利用率 | ≤80% | 正常负载下的CPU使用率 |
| | 内存占用 | ≤2GB | 系统运行时内存使用量 |
| 可靠性指标 | 故障切换时间 | ≤5s | 主备切换完成时间 |
| 扩展性指标 | 最大级联数量 | 8台 | 支持的最大主机级联数量 |
| | 最大外设数量 | 4096个 | 单系统支持的最大外设数 |

### 约束条件

#### 硬件约束条件
- **处理器性能约束**：RK3568四核ARM Cortex-A55，主频1.8GHz
- **内存容量约束**：4GB LPDDR4，需合理分配给各功能模块
- **存储空间约束**：32GB eMMC，需预留系统升级和日志存储空间
- **网口约束**：需支持2个1000Mbps以太网口

#### 软件约束条件
- **实时性约束**：关键控制操作响应时间≤100ms
- **兼容性约束**：需兼容Linux内核版本4.19及以上
- **安全约束**：支持用户认证、权限控制和数据加密

## 硬件架构概述

### 混元主机单机硬件架构
```mermaid
graph TD
    subgraph "混元主机"
        TRUNK[背板]
        IO_1[I/O板卡1]
        IO_2[I/O板卡2]
        MAIN_MASTER[主用主控板卡]
        MAIN_SLAVE[备用主控板卡]
    end
    TX_1[混元TX外设1]
    RX_1[混元RX外设1]
    TX_2[混元TX外设2]
    RX_2[混元RX外设2]

    TRUNK <--> IO_1
    TRUNK <--> IO_2
    TRUNK <--> MAIN_MASTER
    TRUNK <--> MAIN_SLAVE
    IO_1 <--> TX_1
    IO_1 <--> RX_1
    IO_2 <--> TX_2
    IO_2 <--> RX_2
```

混元主机主要由背板、I/O板卡和主控板卡组成。背板负责连接各个板卡并交换数据，I/O板卡用于处理外设设备接入，主控板卡则负责整个系统的控制和管理。
主用主控板卡和备用主控板卡通过热备份的方式实现冗余备份，提高系统的可靠性和可用性。
混元外设与I/O板卡通过光纤链路连接，用于传输音视频数据和控制信号。

### 混元主机主备全冗余硬件架构
```mermaid
graph TD
    subgraph "混元主机1"
        TRUNK_1[背板]
        IO_1[I/O板卡]
        MAIN_MASTER_1[主用主控板卡]
        MAIN_SLAVE_1[备用主控板卡]
    end
    subgraph "混元主机2"
        TRUNK_2[背板]
        IO_2[I/O板卡]
        MAIN_MASTER_2[主用主控板卡]
        MAIN_SLAVE_2[备用主控板卡]
    end
    TX_1[混元TX外设1]
    RX_1[混元RX外设1]
    SWITCH[交换机]

    TRUNK_1 <--> IO_1
    TRUNK_1 <--> MAIN_MASTER_1
    TRUNK_1 <--> MAIN_SLAVE_1
    TRUNK_2 <--> IO_2
    TRUNK_2 <--> MAIN_SLAVE_2
    TRUNK_2 <--> MAIN_MASTER_2
    IO_1 <--> TX_1
    IO_2 <--> RX_1
    MAIN_MASTER_1 <--> SWITCH
    MAIN_SLAVE_1 <--> SWITCH
    MAIN_MASTER_2 <--> SWITCH
    MAIN_SLAVE_2 <--> SWITCH

```
混元主机的主备全冗余架构由两台混元主机组成,两台主机通过交换机连接，实现数据的双向传输和备份。混元外设主用光纤链路和备用光纤链路分别连接到两台主机上，确保主机在任何一台出现故障时，另一台可以继续提供服务。

### 混元主机小主大备硬件架构
```mermaid
graph LR
    MATRIX_1["混元主机1(小)"]
    MATRIX_2["混元主机2(小)"]
    MATRIX_3["混元主机3(大)"]

    TX_1[混元TX外设1]
    TX_2[混元TX外设2]
    RX_1[混元RX外设1]
    RX_2[混元RX外设2]
    SWITCH[交换机]

    MATRIX_1 <--> TX_1
    MATRIX_1 <--> RX_1
    MATRIX_3 <--> TX_1
    MATRIX_3 <--> RX_1
    MATRIX_3 <--> TX_2
    MATRIX_3 <--> RX_2
    MATRIX_2 <--> TX_2
    MATRIX_2 <--> RX_2
    MATRIX_1 <--> SWITCH
    MATRIX_2 <--> SWITCH
    MATRIX_3 <--> SWITCH

```
混元主机的小主大备架构由多台小主机与1台大主机组成，每个外设的主用光纤链路连接到小主机，备用光纤链路连接到大主机，确保系统在小主机故障时，大主机可以接管其所有业务。

### 混元主机级联硬件架构
```mermaid
graph TD
    subgraph "混元主机1"
        TRUNK_1[背板]
        IO_1[I/O板卡]
        GRID_1[级联板卡]
        MAIN_MASTER_1[主用主控板卡]
        MAIN_SLAVE_1[备用主控板卡]
    end
    subgraph "混元主机2"
        TRUNK_2[背板]
        IO_2[I/O板卡]
        GRID_2[级联板卡]
        MAIN_MASTER_2[主用主控板卡]
        MAIN_SLAVE_2[备用主控板卡]
    end

    TX_1[混元TX外设1]
    RX_1[混元RX外设1]
    SWITCH[交换机]

    TRUNK_1 <--> IO_1
    TRUNK_1 <--> GRID_1
    TRUNK_1 <--> MAIN_MASTER_1
    TRUNK_1 <--> MAIN_SLAVE_1
    TRUNK_2 <--> IO_2
    TRUNK_2 <--> GRID_2
    TRUNK_2 <--> MAIN_SLAVE_2
    TRUNK_2 <--> MAIN_MASTER_2
    IO_1 <--> TX_1
    IO_2 <--> RX_1
    GRID_1 <--> GRID_2
    MAIN_MASTER_1 <--> SWITCH
    MAIN_SLAVE_1 <--> SWITCH
    MAIN_MASTER_2 <--> SWITCH
    MAIN_SLAVE_2 <--> SWITCH
```
混元主机的级联架构允许多个混元主机通过级联板卡连接，实现系统的扩展性和灵活性。每台主机可以独立工作，也可以通过级联实现更大规模的KVM交换矩阵。最多支持8台混元主机级联。级联架构下不支持主备主机冗余，支持主备控制板卡冗余。

### 硬件平台规格

| 组件 | 规格参数 | 说明 |
|------|----------|------|
| 主处理器 | RK3568 ARM Cortex-A55 四核 1.8GHz | 主控制处理单元 |
| 系统内存 | 4GB LPDDR4 | 系统运行内存 |
| 存储器 | 32GB eMMC | 系统存储 |
| 网络接口 | 2×千兆以太网口 | 管理和级联通信 |
| 串口 | 2×RS232/485 | 调试和辅助通信 |

### 主控板卡在系统中的定位

主控板卡作为混元主机的核心控制单元，在系统架构中承担以下角色：

1. **系统大脑**：负责整个主机的资源管理、任务调度和业务协调
2. **通信枢纽**：协调背板、I/O板卡、级联板卡和外设间的通信
3. **服务网关**：为外部系统提供API接口和管理服务
4. **可靠性保障**：通过主备冗余机制确保系统高可用性

## 软件总体设计

### 功能需求分析

#### 核心功能需求

混元主机主控板卡软件需要实现以下核心功能：

1. **系统管理功能**
   - 系统初始化和启动管理
   - 硬件资源发现和配置
   - 系统状态监控和健康检查
   - 固件升级和系统维护
   - 用户权限管理和认证

2. **设备通信功能**
   - 与背板的通信和管理
   - 与I/O板卡的通信和管理
   - 与级联板卡的通信和管理
   - 与混元外设的通信和管理
   - 通信协议栈和路由管理

3. **业务控制功能**
   - KVM信号路由控制
   - 坐席业务管理
   - 大屏业务管理
   - 级联业务管理
4. **系统服务功能**
   - RESTful API服务提供
   - TCP API实时控制服务
   - SNMP监控服务
   - Web管理界面服务
   - 系统日志服务

5. **高可用功能**
   - 主备主机冗余管理
   - 小主大备冗余管理
   - 主备控制板卡冗余管理
   - 故障检测和自动切换
   - 数据同步和一致性保障
   - 系统恢复和灾备

#### 性能需求

| 功能模块 | 性能指标 | 目标值 | 说明 |
|----------|----------|--------|------|
| 信号切换 | 响应延迟 | ≤100ms | 从接收命令到完成切换 |
| 故障切换 | 切换时间 | ≤5s | 主备切换完成时间 |
| 内存使用 | 峰值占用 | ≤2GB | 系统运行时最大内存 |
| CPU使用 | 平均负载 | ≤80% | 正常业务负载下CPU使用率 |

### 软件架构设计

#### 架构设计原则

软件架构设计遵循以下原则：

1. **分层架构原则**
   - 采用分层架构，层与层之间职责清晰
   - 上层只能调用下层接口，不能跨层调用
   - 支持层级间的接口抽象和依赖注入

2. **模块化设计原则**
   - 按功能划分模块，模块间松耦合
   - 模块内部高内聚，对外提供清晰接口
   - 支持模块的独立开发、测试
  
3. **可扩展性原则**
   - 采用插件化架构，支持功能模块的动态加载
   - 预留扩展接口，便于后续功能增强
   - 支持多种硬件平台的适配

4. **高可用性原则**
   - 无单点故障设计
   - 支持优雅降级和故障隔离
   - 实现快速故障检测和恢复

5. **嵌入式优化原则**
   - 针对资源受限环境进行优化
   - 最小化内存占用和CPU开销
   - 优化启动时间和响应延迟

#### 整体架构设计

混元主机主控板卡软件采用基于整洁架构(Clean Architecture)的分层模块化设计，结合嵌入式系统特点进行优化。

#### 架构设计图
```mermaid
block-beta
    columns 8
    block: frameworks_layer:8
        columns 7
        TITLE1("框架与驱动层"):7
        REST("REST API服务")
        TCP_API("TCP API服务")
        SNMP("SNMP服务")
        WEB("Web界面")
        HARDWARE("硬件驱动")
        DB("数据库驱动")
        NETWORK("网络驱动")
    end
    space:8
    block: interface_adapters_layer:8
        columns 3
        TITLE2("接口适配器层"):3
        CTRL("控制器")
        GATEWAY("网关")
        REPO("仓库实现")
    end
    space:8
    block: application_layer:8
        columns 8
        TITLE3("应用业务层"):8
        DEVICE_MANAGER("设备管理器")
        CONNECT_MANAGER("连接管理器")
        SEAT_MANAGER("坐席管理器")
        VIDEOWALL_MANAGER("大屏管理器")
        CASCADE_MANAGER("级联管理器")
        REDUNDANCY_MANAGER("冗余管理器")
        DOUBLEBACKUP_MANAGER("双备份管理器")
        MONITOR_MANAGER("监控管理器")
        LOG_MANAGER("日志管理器")
    end
    space:8
    block: core_layer:8
        columns 5
        TITLE4("核心业务层"):5
        EXT("外设实体")
        BOARD("板卡实体")
        MATRIX("主机实体")
        SEAT("坐席实体")
        VIDEOWALL("大屏实体")

    end

    frameworks_layer --> interface_adapters_layer
    interface_adapters_layer --> application_layer
    application_layer --> core_layer
class TITLE1 BT
class TITLE2 BT
class TITLE3 BT
class TITLE4 BT
classDef BT stroke:transparent,fill:transparent

```
#### 模块说明
- **框架与驱动层**：提供系统所需的各种框架和硬件驱动，包括REST API服务、TCP API服务、SNMP服务、Web界面、数据库驱动和网络驱动等。
- **接口适配器层**：实现系统与外部接口的适配，包括适配各个服务的控制器、适配底层通信的网关和适配数据存储的仓库实现。
- **应用业务层**：实现具体的业务逻辑，包括设备管理、坐席管理、大屏管理、级联管理、主机冗余管理、主控板卡冗余管理、监控管理和日志管理等。
- **核心业务层**：定义系统的核心实体，包括外设实体、板卡实体、主机实体、坐席实体和大屏实体等。

### 核心交互流程设计

#### 单主机外设线路交换流程
```mermaid
sequenceDiagram
participant RX as 混元RX外设(I/O 1，端口1)
box 混元主机
participant MAIN as 主控板卡
participant TRUNK as 背板
participant IO1 as I/O板卡1
participant IO2 as I/O板卡2
end
participant TX as 混元TX外设(I/O 2，端口4)

RX ->> MAIN: 请求TX视频
MAIN ->> TRUNK: 配置端口路由
MAIN ->> IO1: 打开端口1输出
MAIN ->> IO2: 打开端口4输入
note over RX,TX: TX的视频数据直通到RX
```
此流程与凯撒中文一致。
#### 单主机外设组播流程
```mermaid
sequenceDiagram
participant RX as 混元RX外设(I/O 1，端口1)
box 混元主机
participant MAIN as 主控板卡
participant TRUNK as 背板
participant IO1 as I/O板卡1
participant IO2 as I/O板卡2
end
participant TX as 混元TX外设(I/O 2，端口4)

RX ->> MAIN: 请求订阅TX子码流1
MAIN ->> TRUNK: 配置I/O 1订阅I/O 2端口4的子码流1
MAIN ->> IO1: 配置端口1订阅I/O 2端口4的子码流1
note over RX,TX: TX的子码流1的包会被背板复制到I/O 1，然后I/O 1复制数据到端口1
```
配置组播时，主控板卡会将订阅信息发送到背板，背板根据订阅信息将数据复制到对应的I/O板卡和端口。这样，多个RX设备可以同时接收同一TX外设的子码流。
**注意**，此流程默认外设发送组播数据时无须打开端口输出。
#### 单主机外设单播流程
单主机下外设单播无须经过主控软件控制，外设可以直接通过背板进行数据传输。

#### 跨主机线路交换流程
```mermaid
sequenceDiagram
participant RX as 混元RX外设(I/O 1，端口1)
box 混元主机1(级联Leader)
participant MAIN1 as 主控板卡
participant TRUNK1 as 背板
participant IO1 as I/O板卡1
participant GRID1 as 级联板卡1
end
box 混元主机2(级联Follower)
participant MAIN2 as 主控板卡
participant TRUNK2 as 背板
participant IO2 as I/O板卡2
participant GRID2 as 级联板卡2
end
participant TX as 混元TX外设(I/O 2，端口4)

RX ->> MAIN1: 请求TX视频
MAIN1 ->> MAIN1: 计算级联路径
MAIN1 ->> +MAIN2: 配置级联路由到主机2
MAIN2 ->> TRUNK2: 配置TX到级联端口的路由
MAIN2 ->> IO2: 打开端口4输入
MAIN2 ->> GRID2: 打开级联端口输出
MAIN2 -->> -MAIN1: 确认路由配置完成
MAIN1 ->> TRUNK1: 配置级联端口到RX的路由
MAIN1 ->> GRID1: 打开级联端口输入
MAIN1 ->> IO1: 打开端口1输出

note over RX,TX: TX的视频数据直通到RX
```
此流程中，主控板卡需要计算跨主机的级联路径，并配置级联路由到从机主控板卡。然后从机主控板卡配置TX外设到级联端口的路由，并打开对应的输入和输出端口。最后，主用主控板卡配置背板和I/O板卡的路由，实现数据传输。
#### 跨主机单播流程
```mermaid
sequenceDiagram
participant RX as 混元RX外设(I/O 1，端口1)
box 混元主机1(级联Leader)
participant MAIN1 as 主控板卡
participant TRUNK1 as 背板
participant IO1 as I/O板卡1
participant GRID1 as 级联板卡1
end
box 混元主机2(级联Follower)
participant MAIN2 as 主控板卡
participant TRUNK2 as 背板
participant IO2 as I/O板卡2
participant GRID2 as 级联板卡2
end
participant TX as 混元TX外设(I/O 2，端口4)

RX ->> MAIN1: 请求与TX单播通信
MAIN1 ->> MAIN1: 计算级联路径，RX的端口映射到级联板卡2的端口1，TX的端口映射到级联板卡2的端口1
MAIN1 ->> +MAIN2: 配置级联路由到主机2
MAIN2 ->> TX: 广播更新端口映射
MAIN2 -->> -MAIN1: 确认路由配置完成
MAIN1 ->> RX: 广播更新端口映射

note over RX,TX: RX和TX的端口映射关系已更新，RX可以直接与TX通信,数据包的路由地址需要增加端口映射信息
```
此流程中，主控板卡需要计算跨主机的级联路径，并配置级联路由到从机主控板卡。然后从机主控板卡广播更新端口映射信息。最后，主用主控板卡广播更新端口映射信息，实现数据传输。
#### 跨主机组播流程
```mermaid
sequenceDiagram
participant RX as 混元RX外设(I/O 1，端口1)
box 混元主机1(级联Leader)
participant MAIN1 as 主控板卡1
participant TRUNK1 as 背板1
participant IO1 as I/O板卡1
participant GRID1 as 级联板卡1
end
box 混元主机2(级联Follower)
participant MAIN2 as 主控板卡2
participant TRUNK2 as 背板2
participant IO2 as I/O板卡2
participant GRID2 as 级联板卡2
end
participant TX as 混元TX外设(I/O 2，端口4)

RX ->> MAIN1: 请求订阅TX子码流1
MAIN1 ->> MAIN1: 计算级联路径
MAIN1 ->> +MAIN2: 配置级联路由到主机2
MAIN2 ->> TRUNK2: 配置级联板卡2订阅I/O 2端口4的子码流1
MAIN2 ->> GRID2: 配置端口1订阅I/O 2端口4的子码流1
MAIN2 -->> -MAIN1: 确认路由配置完成
MAIN1 ->> TRUNK1: 配置I/O 1订阅级联板卡1端口1的子码流1
MAIN1 ->> IO1: 配置端口1订阅级联板卡1端口1的子码流1

note over RX,TX: TX的子码流1的包的数据流程流向：TX->I/O板卡2->背板2->级联板卡2->级联板卡1->背板1->I/O板卡1->RX
```
在跨主机组播流程中，主控板卡需要计算跨主机的级联路径，并配置级联路由到从机主控板卡。然后从机主控板卡配置订阅信息，并将数据复制到对应的I/O板卡和端口。这样，多个RX设备可以同时接收同一TX外设的子码流。

### 技术选型

#### 开发语言与框架

- **主要开发语言**：Rust、C++17
  - 高性能，适合底层硬件驱动和实时数据处理
  - 内存管理可控，适合嵌入式环境
  - 丰富的生态系统和第三方库支持

- **Web界面开发**：
  - 前端：Vue.js 3 + TypeScript + Element Plus，通过标准浏览器访问
  - 客户端：Electron，可从主控板卡下载到本地运行

#### 数据存储

- **数据库**：SQLite
  - 轻量级，适合嵌入式环境
  - 支持事务，数据一致性好
  - 无需独立数据库服务进程

- **ORM框架**：SeaORM
  - Rust语言的ORM框架
  - 支持异步操作
  - 简化数据库操作

- **数据序列化**：Cap'n Proto
  - 高性能二进制序列化
  - 跨语言支持
  - 版本兼容性好

#### 网络通信

- **HTTP服务**：Axum
  - Rust语言的Web框架
  - 异步非阻塞
  - 高性能

- **TCP/UDP通信**：Tokio
  - 异步I/O操作
  - 跨平台支持
  - 高性能网络编程
  
- **SNMP库**：snmp
  - Rust语言的SNMP库
  - 支持SNMPv1/v2c/v3
  - 提供设备状态监控和告警功能

#### 日志与监控

- **日志框架**：tracing
  - Rust语言的日志框架
  - 支持异步日志记录
  - 可配置的日志级别和输出格式

### 接口设计

系统接口分为对外接口和内部接口两类，为第三方集成、Web控制以及内部模块协作提供统一的访问方式。

#### 对外接口

**RESTful API**
- 基于HTTPS协议，JSON数据格式
- 主要用于配置管理、状态查询等非实时操作
- 支持Token认证和权限控制
- 遵循RESTful设计原则

**TCP API**  
- 基于TCP长连接的自定义字符串协议
- 用于低延迟、高实时性的控制操作
- 支持KVM切换、信号源切换等实时控制

**SNMP接口**
- 支持标准SNMP协议
- 提供设备状态监控和告警功能
- 便于与第三方网管系统集成

#### 内部接口

**模块间通信接口**
- 定义各业务模块间的标准接口
- 采用依赖注入模式，降低模块耦合度
- 支持接口模拟，便于单元测试

**硬件抽象接口**  
- 封装底层硬件驱动细节
- 提供统一的板卡操作接口
- 支持不同硬件版本的兼容性

### 数据设计

#### 数据存储架构

**存储方案**
- 主要存储：SQLite关系数据库
- 序列化：Cap'n Proto二进制序列化
- 日志存储：文本文件 + 日志轮转

**数据访问层**
- ORM框架封装数据库操作
- 统一的数据访问接口
- 支持事务管理和数据验证

#### 核心数据模型

系统核心实体及其主要属性：

- **主机实体**：主机ID、名称、IP地址、板卡配置
- **板卡实体**：槽位号、类型、状态、能力描述
- **外设实体**：设备ID、类型、连接端口、状态信息
- **坐席实体**：坐席ID、关联设备、权限配置
- **大屏实体**：屏幕布局、窗口配置、信号源映射
- **连接实体**：连接类型、源端、目标端、路由信息
- **用户实体**：用户账号、权限角色、认证信息

## 可靠性设计

### 冗余设计

#### 主备控制板卡冗余（板卡级冗余）

1. **冗余架构**
   - 主备双控制板卡热备份方式，位于同一主机内，使用不同的物理IP
   - 主控承担业务处理，备控实时同步状态
   - 故障时自动切换，业务连续性保障
   - 使用keepalived等机制实现统一虚拟IP访问

2. **状态同步机制**
   ```
   主控板卡 ←→ [背板高速通道] ←→ 备控板卡
      ↓                           ↓
   配置数据同步               状态数据同步
   业务状态同步               心跳检测机制（双向心跳）
   ```

3. **切换策略**
   - 背板上报控制板卡心跳状态，主控与备控均监测对方心跳
   - 主控故障时自动切换到备控，切换过程对业务透明
   - 切换过程中保持业务连续性

#### 主备主机全冗余（主机级冗余）

1. **全冗余架构**
   - 双主机热备份，外设双路连接，外设需支持双链路自动切换
   - 主机故障时外设自动切换到备机，业务不中断
   - 支持地理分离部署，灾难恢复
   - 使用keepalived等机制实现统一虚拟IP访问

2. **数据同步**
   ```
   主机A ←→ [网络通信] ←→ 主机B
     ↓                     ↓
   业务数据同步         业务数据同步
   ```
   - 主机间同步以业务数据为主，确保状态一致性

3. **切换策略**
   - 主控板卡定时发送心跳到外设，外设根据心跳状态与链路状态自动切换到备机
   - 特定业务逻辑下，可通过指令触发切换
   - 切换过程对外设和业务透明

#### 小主大备冗余（主机级冗余）

1. **小主大备架构**
   - 小主机作为业务处理节点，大主机作为备份节点
   - 小主机故障时业务自动切换到大主机
   - 大主机承担所有小主机的业务，支持大规模外设接入

2. **数据同步**
   ```
   小主机A ←→ [网络通信] ←→ 大主机 ←→ [网络通信] ←→ 小主机B
     ↓                     ↓                     ↓
   业务数据同步         业务数据同步         业务数据同步
   ```
   - 小主机与大主机间同步以业务数据为主，确保状态一致性

3. **切换策略**
   - 主控板卡定时发送心跳到外设，外设根据心跳状态与链路状态自动切换到备机
   - 特定业务逻辑下，可通过指令触发切换
   - 切换过程对外设和业务透明

### 故障检测与处理

#### 故障检测机制

1. **硬件故障检测**
   - 板卡温度和负载监控
   - 背板通信链路检测

2. **软件故障检测**
   - 进程状态监控和看门狗
   - 关键服务健康检查
   - 内存泄漏和资源耗尽检测

3. **业务故障检测**
   - 信号切换超时检测
   - 通信协议异常检测
   - 数据一致性检验
   - 外设连接状态检测

#### 故障处理策略

1. **自动恢复**
   - 进程异常退出自动重启
   - 网络连接断开自动重连
   - 临时性故障自动重试

## 安全设计

### 安全需求

系统需要满足以下安全要求：
- 防止未授权访问和操作
- 保护数据传输和存储安全
- 追踪系统操作

### 身份认证与授权

#### 用户认证机制

1. **本地认证**
   - 用户名/密码认证
   - 支持密码复杂度策略
   - 会话超时管理

#### 权限管理

1. **角色权限模型**
   ```
   用户 → 角色 → 权限 → 资源
   ↓      ↓      ↓      ↓
   管理员  系统管理 所有操作 全部资源
   操作员  业务操作 基础操作 部分资源
   观察员  只读权限 查看权限 状态信息
   ```

2. **细粒度权限控制**
   - 功能级权限控制

### 网络安全
#### 数据传输安全

1. **加密通信**
   - HTTPS加密传输
   - SSH密钥认证
   - SNMPv3加密认证
   - TCP API自定义加密

### 安全审计

#### 日志审计

1. **操作日志**
   - 用户登录/登出记录
   - 系统配置变更记录
   - 业务操作记录
   - 异常事件记录

2. **安全日志**
   - 认证失败记录

## 测试设计

### 测试策略

#### 测试层次

1. **单元测试**
   - 目标覆盖率：≥80%，采用lcov/gcov等工具统计，覆盖核心算法和业务逻辑
   - 重点测试核心算法和业务逻辑
   - 模拟依赖组件进行隔离测试
   - 自动化测试集成到CI/CD流程

2. **集成测试**
   - 模块间接口测试
   - 硬件抽象层集成测试
   - 数据库集成测试
   - 网络通信集成测试
   - 软硬件联调测试

3. **系统测试**
   - 性能基准测试
   - 可靠性测试
   - 兼容性测试

#### 测试类型

1. **功能测试**
   - 正向功能测试
   - 边界条件测试
   - 异常情况测试
   - 错误处理测试

2. **非功能测试**
   - 性能测试
   - 压力测试

## 部署与维护

### 升级设计

#### 升级策略

1. **主控板卡在线升级**
   - 主备切换升级
   - 分阶段滚动升级
   - 升级过程业务无中断
   - 升级包完整性校验，防止篡改
   - 升级失败自动回退，保障系统可用性

2. **背板、I/O板卡升级**
   - 支持批量升级
   - 升级后自动验证
   - 升级失败自动回退
   - 升级过程业务影响最小化，关键业务可平滑迁移
3. **外设升级**
   - 支持批量升级
   - 升级后自动验证

### 运维监控

#### 系统监控

1. **关键指标监控**
   - 系统资源使用情况
   - 应用性能指标
   - 业务健康状态
   - 安全事件监控
   - 远程运维与监控支持

#### 日志管理

1. **日志分类**
   - 系统日志：系统运行状态
   - 应用日志：应用程序日志
   - 业务日志：业务操作记录
   - 安全日志：安全事件记录

2. **日志处理**
   - 日志轮转和压缩
   - 日志远程存储

## 总结

### 设计特点

本概要设计文档针对混元架构平台主控软件的特点，采用了以下设计策略：

1. **嵌入式优化**：针对资源受限的嵌入式环境，在架构设计、算法选择、内存管理等方面进行了专门的优化

2. **高可靠性**：通过主备冗余、故障检测、自动切换等机制，确保系统的高可用性和业务连续性

3. **模块化架构**：采用分层模块化设计，便于开发、测试、维护和功能扩展

4. **性能优化**：通过异步I/O、缓存机制、算法优化等手段，满足实时性和高并发的性能要求

5. **安全设计**：建立完善的身份认证、权限控制、数据加密机制

### 技术创新点

1. **整洁架构在嵌入式系统的应用**：将整洁架构思想应用到嵌入式系统设计中，提高了系统的可维护性和可扩展性

2. **多层次冗余设计**：设计了主机级、系统级多层次的冗余机制，大幅提升了系统可靠性

3. **高效的数据同步机制**：基于Cap'n Proto序列化和背板高速通道，实现了高效的主备数据同步

### 风险与挑战

1. **硬件兼容性风险**：多种硬件平台适配难度大，需充分测试
2. **主备同步一致性风险**：极端情况下主备状态可能短暂不一致，需完善同步与回退机制
3. **性能瓶颈风险**：高并发场景下可能出现性能瓶颈，需持续优化
4. **升级与回滚风险**：升级失败可能影响业务，需完善回滚与验证机制

### 后续工作

基于本概要设计，后续需要开展以下工作：

1. **详细设计**：针对各个模块进行详细的设计，包括接口定义、算法实现、数据结构等
2. **原型开发**：开发核心功能原型，验证关键技术方案的可行性
3. **开发实现**：按照设计文档进行软件开发实现
4. **测试验证**：进行全面的功能测试、性能测试和可靠性测试
5. **系统集成**：与硬件平台和其他系统组件进行集成测试
6. **用户文档与培训**：编写用户手册和开发文档，开展用户培训
7. **持续集成与交付（CI/CD）流程完善**：完善自动化构建、测试、部署流程，提升交付效率和质量

通过严格按照本概要设计执行后续工作，可以确保混元架构平台主控软件达到设计目标，满足用户需求。
